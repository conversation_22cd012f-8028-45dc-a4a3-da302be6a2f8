{"app": {"name": "Hiel Physics", "welcome": "Bienvenue dans Hiel Physics"}, "onboarding": {"language": {"title": "Choisissez votre langue préférée", "description": "Veuillez sélectionner la langue que vous préférez utiliser dans l'application", "arabic": "العربية", "french": "Français", "select_language": "Choisir la langue"}, "track": {"title": "Choisissez votre filière d'études", "description": "Veuillez sélectionner la filière dans laquelle vous étudiez", "select_track": "Choisir la filière", "tracks": {"sciences_maths_a": {"name": "Sciences Mathématiques A", "description": "Filière Sciences Mathématiques - Option A"}, "sciences_maths_b": {"name": "Sciences Mathématiques B", "description": "Filière Sciences Mathématiques - Option B"}, "sciences_physiques": {"name": "Sciences Physiques", "description": "Filière Sciences Physiques"}, "svt": {"name": "Sciences de la Vie et de la Terre", "description": "Filière Sciences de la Vie et de la Terre"}, "sciences_techniques_electriques": {"name": "Sciences Techniques Électriques", "description": "Filière Sciences Techniques Électriques"}, "sciences_techniques_mecaniques": {"name": "Sciences Techniques Mécaniques", "description": "Filière Sciences Techniques Mécaniques"}, "sciences_economiques": {"name": "Sciences Économiques", "description": "Filière Sciences Économiques"}, "lettres": {"name": "Lettres", "description": "<PERSON><PERSON><PERSON>"}}}}, "common": {"buttons": {"next": "Suivant", "back": "Précédent", "finish": "<PERSON><PERSON><PERSON>", "cancel": "Annuler", "save": "Enregistrer", "continue": "<PERSON><PERSON><PERSON>", "skip": "<PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON><PERSON>", "ok": "OK", "yes": "O<PERSON>", "no": "Non"}, "messages": {"loading": "Chargement...", "saving": "Enregistrement...", "saved": "Enregistré avec succès", "error": "Une erreur s'est produite", "success": "Su<PERSON>ès", "please_wait": "<PERSON><PERSON><PERSON><PERSON>er", "select_option": "Veuillez sélectionner une option", "required_field": "Ce champ est requis"}, "navigation": {"home": "Accueil", "courses": "Cours", "quizzes": "Quiz", "exercises": "Exercices", "exams": "Examens", "simulations": "Simulations", "settings": "Paramètres", "about": "À propos", "help": "Aide"}, "explore": "Explorer", "days_ago": "jours passés", "week_ago": "semaine passée"}, "home": {"title": "Accueil", "welcome_message": "Bienvenue dans l'application Hiel Physics", "description": "Application éducative spécialisée en physique pour les étudiants marocains", "get_started": "Commencer", "explore": "Explorer", "recent_lessons": "Leçons récentes", "continue_learning": "Continuer l'apprentissage", "quick_access": "Accès rapide", "statistics": "Statistiques", "recent_activity": "Activité récente", "courses_description": "Cours de physique par filières", "quizzes_description": "Quiz interactifs courts", "exercises_description": "Exercices résolus et variés", "simulations_description": "Simulations interactives de phénomènes", "tracks": "Filières", "courses": "Cours", "exercises": "Exercices", "simulations": "Simulations", "recent_course": "Cours récent", "completed_quiz": "Quiz terminé", "solved_exercise": "Exer<PERSON><PERSON>", "mechanics_chapter": "Chapitre mécanique", "thermodynamics_quiz": "Quiz thermodynamique", "kinematics_problem": "Problème de cinématique", "yesterday": "<PERSON>er", "two_days_ago": "Il y a deux jours", "three_days_ago": "Il y a trois jours"}, "courses": {"description": "Explorez les cours de physique organisés par filières d'études", "physics": "Physique", "mathematics": "Mathématiques", "chemistry": "<PERSON><PERSON>", "physics_description": "Cours de physique fondamentale et avancée", "mathematics_description": "Mathématiques appliquées à la physique", "chemistry_description": "Chimie physique et appliquée", "recent": "Cours récents", "recent_course_1": "Mécanique classique", "recent_course_2": "Électricité et magnétisme", "recent_course_3": "Optique géométrique", "progress_70": "Progrès: 70%", "progress_45": "Progrès: 45%", "progress_90": "Progrès: 90%"}, "quizzes": {"description": "Testez vos connaissances avec des quiz interactifs courts", "completed": "Te<PERSON>in<PERSON>", "average_score": "Score moyen", "available": "Disponibles", "available_quizzes": "Quiz disponibles", "mechanics_quiz": "Quiz mécanique", "thermodynamics_quiz": "Quiz thermodynamique", "questions_15": "15 questions", "questions_20": "20 questions", "duration_20min": "20 minutes", "duration_25min": "25 minutes", "start_quiz": "Commencer le quiz", "recent_results": "Résultats récents", "optics_quiz": "Quiz optique", "waves_quiz": "Quiz ondes", "electricity_quiz": "Quiz électricité", "score_92": "Score: 92%", "score_78": "Score: 78%", "score_65": "Score: 65%"}, "exercises": {"description": "Entraînez-vous à résoudre des problèmes et exercices variés", "problem_solving": "Résolution de problèmes", "lab_simulations": "Simulations de laboratoire", "problem_solving_description": "Exercices théoriques et appliqués", "lab_simulations_description": "Expériences virtuelles interactives", "practice": "<PERSON>'entraîner", "progress": "Progrès", "completed": "Te<PERSON>in<PERSON>", "accuracy": "Précision", "streak": "Série", "recent": "Exercices récents", "kinematics_problems": "Problèmes de cinématique", "energy_conservation": "Conservation de l'énergie", "wave_interference": "Interférence des ondes", "difficulty_medium": "<PERSON><PERSON><PERSON>", "difficulty_hard": "Difficile", "difficulty_easy": "Facile"}, "settings": {"title": "Paramètres", "description": "Personnalisez les paramètres de l'application et vos préférences", "appearance": "Apparence", "language_region": "Langue et région", "language": "<PERSON><PERSON>", "language_description": "Choisissez la langue de l'interface", "educational_track": "Filière d'études", "track_description": "Choisissez votre filière d'études", "notifications": "Notifications", "push_notifications": "Notifications push", "push_description": "Recevoir des notifications sur les mises à jour", "email_notifications": "Notifications par email", "email_description": "Recevoir des emails de notification", "privacy_security": "Confidentialité et sécurité", "data_collection": "Collecte de don<PERSON>", "data_description": "Autoriser la collecte de données d'utilisation", "clear_data": "Efface<PERSON> les données", "clear_description": "Supprimer toutes les données locales", "clear": "<PERSON><PERSON><PERSON><PERSON>", "about": "À propos", "version": "Version", "privacy_policy": "Politique de confidentialité", "terms_of_service": "Conditions d'utilisation", "theme": "Thème", "theme_description": "Choisissez le thème de l'application"}, "exams": {"description": "Préparez-vous aux examens nationaux et régionaux", "upcoming": "Examens à venir", "midterm_physics": "Examen de physique semestriel", "date_march_15": "15 mars 2024", "duration_2hours": "Durée: 2 heures", "prepare": "<PERSON> préparer", "statistics": "Statistiques", "completed": "Te<PERSON>in<PERSON>", "average_score": "Score moyen", "current_grade": "Note actuelle", "past_exams": "Examens passés", "mechanics_final": "Examen final de mécanique", "thermodynamics_midterm": "Examen semestriel de thermodynamique", "optics_quiz": "Quiz d'optique", "score_94": "Score: 94%", "score_82": "Score: 82%", "score_76": "Score: 76%", "study_resources": "Ressources d'étude", "study_guides": "Guides d'étude", "practice_tests": "Tests d'entraînement", "download": "Télécharger", "start": "Commencer"}, "simulations": {"description": "Explorez les phénomènes physiques avec des simulations interactives", "featured": "Simulations en vedette", "wave_interference": "Interférence des ondes", "wave_description": "Simulation d'interférence des ondes sonores et lumineuses", "electric_field": "Champ électrique", "electric_description": "Visualisation des champs électriques et des forces", "launch": "Lancer", "categories": "Catégories", "mechanics": "Mécanique", "thermodynamics": "Thermodynamique", "optics": "Optique", "simulations": "simulations", "recent": "Simulations récentes", "pendulum_motion": "Mouvement du pendule", "projectile_motion": "Mouvement de projectile", "circuit_builder": "Constructeur de circuits", "last_used_2_days": "Dernière utilisation: il y a 2 jours", "last_used_5_days": "Dernière utilisation: il y a 5 jours", "last_used_1_week": "Dernière utilisation: il y a 1 semaine"}, "theme": {"light": "<PERSON>", "dark": "Sombre", "system": "Système", "toggle_tooltip": "Changer le thème"}, "layout": {"expand_navigation": "É<PERSON>re <PERSON>", "collapse_navigation": "Réduire la navigation", "menu": "<PERSON><PERSON>"}, "profile": {"menu": "Profil", "settings": "Paramètres du profil", "logout": "Se déconnecter"}, "errors": {"network_error": "Erreur de connexion réseau", "loading_error": "Erreur de chargement des données", "save_error": "Erreur d'enregistrement des données", "unknown_error": "<PERSON><PERSON><PERSON> inconnue", "validation_error": "Erreur de validation des données"}}