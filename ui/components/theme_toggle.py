"""
Theme toggle component for switching between light, dark, and system themes.
"""

import flet as ft
from typing import Optional

from services.theme_service import ThemeService, ThemeMode
from services.i18n import I18nService


def create_theme_toggle(theme_service: ThemeService, i18n_service: I18nService) -> ft.Control:
    """Create a theme toggle dropdown.

    Args:
        theme_service: Theme service
        i18n_service: Internationalization service

    Returns:
        Theme toggle dropdown control
    """
    current_mode = theme_service.get_theme_mode()

    def handle_theme_change(e):
        """Handle theme mode change."""
        try:
            new_mode = ThemeMode(e.control.value)
            theme_service.set_theme_mode(new_mode)
        except ValueError:
            # Invalid theme mode, reset to current
            e.control.value = theme_service.get_theme_mode().value
            e.control.update()

    # Create dropdown options
    options = [
        ft.dropdown.Option(
            key=ThemeMode.LIGHT.value,
            text=i18n_service.t("theme.light")
        ),
        ft.dropdown.Option(
            key=ThemeMode.DARK.value,
            text=i18n_service.t("theme.dark")
        ),
        ft.dropdown.Option(
            key=ThemeMode.SYSTEM.value,
            text=i18n_service.t("theme.system")
        )
    ]

    # Create dropdown
    return ft.Dropdown(
        value=current_mode.value,
        options=options,
        width=140,
        height=40,
        text_size=14,
        content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
        on_change=handle_theme_change,
        tooltip=i18n_service.t("theme.toggle_tooltip")
    )


# Legacy class for compatibility
class ThemeToggle:
    """Legacy theme toggle class."""

    def __init__(self, theme_service: ThemeService, i18n_service: I18nService):
        self.theme_service = theme_service
        self.i18n_service = i18n_service
        self.control = create_theme_toggle(theme_service, i18n_service)

    def build(self) -> ft.Control:
        return self.control
