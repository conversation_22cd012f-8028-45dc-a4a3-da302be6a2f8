"""
Reusable app bar component with consistent top navigation.
"""

import flet as ft
from typing import Optional, Callable, List

from services.i18n import I18nService
from services.theme_service import ThemeService
from ui.components.theme_toggle import ThemeToggle


class AppBar(ft.Control):
    """Reusable app bar component for consistent top navigation."""
    
    def __init__(
        self,
        page: ft.Page,
        i18n_service: I18nService,
        theme_service: ThemeService,
        title: Optional[str] = None,
        show_menu_button: bool = False,
        on_menu_click: Optional[Callable[[], None]] = None,
        actions: Optional[List[ft.Control]] = None
    ):
        """Initialize the app bar.
        
        Args:
            page: Flet page instance
            i18n_service: Internationalization service
            theme_service: Theme service
            title: App bar title
            show_menu_button: Whether to show menu button
            on_menu_click: Callback for menu button click
            actions: Additional action controls
        """
        super().__init__()
        self.page = page
        self.i18n_service = i18n_service
        self.theme_service = theme_service
        self.title = title or self.i18n_service.t("app.name")
        self.show_menu_button = show_menu_button
        self.on_menu_click = on_menu_click
        self.actions = actions or []
        
        # App bar component
        self.app_bar: Optional[ft.AppBar] = None
        
        # Listen for theme changes
        self.theme_service.add_theme_change_listener(self._on_theme_change)
        
        # Listen for language changes
        self.i18n_service.add_language_change_listener(self._on_language_change)
    
    def build(self) -> ft.Control:
        """Build the app bar."""
        # Create leading widget
        leading = None
        if self.show_menu_button:
            leading = ft.IconButton(
                icon=ft.Icons.MENU,
                on_click=self._handle_menu_click,
                tooltip=self.i18n_service.t("layout.menu")
            )
        
        # Create default actions
        default_actions = [
            # Theme toggle
            ThemeToggle(
                theme_service=self.theme_service,
                i18n_service=self.i18n_service
            ),
            
            # Language selector (placeholder)
            ft.PopupMenuButton(
                icon=ft.Icons.LANGUAGE,
                tooltip=self.i18n_service.t("settings.language"),
                items=[
                    ft.PopupMenuItem(
                        text="العربية",
                        on_click=lambda e: self._handle_language_change("ar")
                    ),
                    ft.PopupMenuItem(
                        text="Français",
                        on_click=lambda e: self._handle_language_change("fr")
                    )
                ]
            ),
            
            # Profile menu (placeholder)
            ft.PopupMenuButton(
                icon=ft.Icons.ACCOUNT_CIRCLE,
                tooltip=self.i18n_service.t("profile.menu"),
                items=[
                    ft.PopupMenuItem(
                        text=self.i18n_service.t("profile.settings"),
                        icon=ft.Icons.SETTINGS
                    ),
                    ft.PopupMenuItem(
                        text=self.i18n_service.t("profile.logout"),
                        icon=ft.Icons.LOGOUT
                    )
                ]
            )
        ]
        
        # Combine with custom actions
        all_actions = self.actions + default_actions
        
        # Create app bar
        self.app_bar = ft.AppBar(
            leading=leading,
            leading_width=56,
            title=ft.Text(
                self.title,
                size=20,
                weight=ft.FontWeight.W500
            ),
            center_title=False,
            bgcolor=self.theme_service.get_theme_colors()["surface"],
            surface_tint_color=self.theme_service.get_theme_colors()["surface_tint"],
            actions=all_actions,
            elevation=0
        )
        
        return self.app_bar
    
    def set_title(self, title: str) -> None:
        """Set the app bar title.
        
        Args:
            title: New title
        """
        self.title = title
        if self.app_bar and self.app_bar.title:
            self.app_bar.title.value = title
            self.update()
    
    def add_action(self, action: ft.Control) -> None:
        """Add an action to the app bar.
        
        Args:
            action: Action control to add
        """
        self.actions.append(action)
        self.update()
    
    def _handle_menu_click(self, e) -> None:
        """Handle menu button click.
        
        Args:
            e: Click event
        """
        if self.on_menu_click:
            self.on_menu_click()
    
    def _handle_language_change(self, language: str) -> None:
        """Handle language change.
        
        Args:
            language: Language code
        """
        self.i18n_service.set_language(language)
    
    def _on_theme_change(self) -> None:
        """Handle theme change."""
        self.update()
    
    def _on_language_change(self) -> None:
        """Handle language change."""
        self.update()
    
    def will_unmount(self) -> None:
        """Called when control is unmounted."""
        # Remove listeners
        self.theme_service.remove_theme_change_listener(self._on_theme_change)
        self.i18n_service.remove_language_change_listener(self._on_language_change)
