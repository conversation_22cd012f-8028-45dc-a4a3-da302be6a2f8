"""
Main application layout using Shell architecture pattern.
"""

import flet as ft
from typing import Optional, Callable

from config.settings import (
    NAVIGATION_ITEMS, RESPONSIVE_BREAKPOINTS, LAYOUT_DIMENSIONS,
    ANIMATION_DURATION_MS
)
from services.i18n import I18nService
from services.theme_service import ThemeService
from ui.components.navigation_rail import NavigationRail
from ui.components.navigation_drawer import NavigationDrawer
from ui.components.app_bar import AppBar
from ui.utils.responsive_utils import get_screen_size_category, should_use_drawer
from ui.utils.navigation_utils import route_to_navigation_item, get_selected_index


class MainLayout(ft.Control):
    """Main application layout with sidebar navigation and content area."""
    
    def __init__(
        self,
        page: ft.Page,
        i18n_service: I18nService,
        theme_service: ThemeService,
        on_navigation_click: Optional[Callable[[str], None]] = None
    ):
        """Initialize the main layout.
        
        Args:
            page: Flet page instance
            i18n_service: Internationalization service
            theme_service: Theme service
            on_navigation_click: Callback for navigation clicks
        """
        super().__init__()
        self.page = page
        self.i18n_service = i18n_service
        self.theme_service = theme_service
        self.on_navigation_click = on_navigation_click
        
        # Layout components
        self.navigation_rail: Optional[NavigationRail] = None
        self.navigation_drawer: Optional[NavigationDrawer] = None
        self.app_bar: Optional[AppBar] = None
        self.content_view: Optional[ft.Control] = None
        
        # Layout state
        self.selected_index = 0
        self.is_drawer_open = False
        
        # Listen for theme changes
        self.theme_service.add_theme_change_listener(self._on_theme_change)
        
        # Listen for language changes
        self.i18n_service.add_language_change_listener(self._on_language_change)
    
    def build(self) -> ft.Control:
        """Build the main layout."""
        # Determine if we should use drawer or rail
        use_drawer = should_use_drawer(self.page.width)
        
        if use_drawer:
            return self._build_drawer_layout()
        else:
            return self._build_rail_layout()
    
    def _build_rail_layout(self) -> ft.Control:
        """Build layout with navigation rail for desktop."""
        # Create navigation rail
        self.navigation_rail = NavigationRail(
            page=self.page,
            i18n_service=self.i18n_service,
            theme_service=self.theme_service,
            selected_index=self.selected_index,
            on_destination_click=self._handle_navigation_click
        )
        
        # Create app bar
        self.app_bar = AppBar(
            page=self.page,
            i18n_service=self.i18n_service,
            theme_service=self.theme_service,
            show_menu_button=False
        )
        
        # Create main content area
        content_area = ft.Container(
            content=ft.Column([
                self.app_bar,
                ft.Container(
                    content=self.content_view or ft.Container(),
                    expand=True,
                    padding=LAYOUT_DIMENSIONS["content_padding"]
                )
            ]),
            expand=True
        )
        
        # Return row layout with rail and content
        return ft.Row([
            self.navigation_rail,
            ft.VerticalDivider(width=1),
            content_area
        ], expand=True, spacing=0)
    
    def _build_drawer_layout(self) -> ft.Control:
        """Build layout with navigation drawer for mobile/tablet."""
        # Create navigation drawer
        self.navigation_drawer = NavigationDrawer(
            page=self.page,
            i18n_service=self.i18n_service,
            theme_service=self.theme_service,
            selected_index=self.selected_index,
            on_item_click=self._handle_navigation_click,
            on_dismiss=self._handle_drawer_dismiss
        )
        
        # Create app bar with menu button
        self.app_bar = AppBar(
            page=self.page,
            i18n_service=self.i18n_service,
            theme_service=self.theme_service,
            show_menu_button=True,
            on_menu_click=self._handle_menu_click
        )
        
        # Create main content area
        content_area = ft.Container(
            content=ft.Column([
                self.app_bar,
                ft.Container(
                    content=self.content_view or ft.Container(),
                    expand=True,
                    padding=LAYOUT_DIMENSIONS["content_padding"]
                )
            ]),
            expand=True
        )
        
        # Return stack with drawer overlay
        return ft.Stack([
            content_area,
            self.navigation_drawer
        ], expand=True)
    
    def set_content_view(self, view: ft.Control) -> None:
        """Set the content view.
        
        Args:
            view: Content view to display
        """
        self.content_view = view
        self.update()
    
    def update_selected_item(self, route: str) -> None:
        """Update the selected navigation item based on route.
        
        Args:
            route: Current route
        """
        new_index = get_selected_index(route)
        if new_index != self.selected_index:
            self.selected_index = new_index
            
            # Update navigation components
            if self.navigation_rail:
                self.navigation_rail.set_selected_index(self.selected_index)
            if self.navigation_drawer:
                self.navigation_drawer.set_selected_index(self.selected_index)
    
    def _handle_navigation_click(self, index: int) -> None:
        """Handle navigation item click.
        
        Args:
            index: Index of clicked navigation item
        """
        if index < len(NAVIGATION_ITEMS):
            navigation_item = NAVIGATION_ITEMS[index]
            route = navigation_item.route
            
            # Close drawer if open
            if self.navigation_drawer and self.is_drawer_open:
                self.navigation_drawer.hide()
                self.is_drawer_open = False
            
            # Call navigation callback
            if self.on_navigation_click:
                self.on_navigation_click(route)
    
    def _handle_menu_click(self) -> None:
        """Handle menu button click (toggle drawer)."""
        if self.navigation_drawer:
            if self.is_drawer_open:
                self.navigation_drawer.hide()
                self.is_drawer_open = False
            else:
                self.navigation_drawer.show()
                self.is_drawer_open = True
    
    def _handle_drawer_dismiss(self) -> None:
        """Handle drawer dismiss."""
        self.is_drawer_open = False
    
    def _on_theme_change(self) -> None:
        """Handle theme change."""
        self.update()
    
    def _on_language_change(self) -> None:
        """Handle language change."""
        self.update()
    
    def did_mount(self) -> None:
        """Called when control is mounted."""
        # Listen for page resize
        self.page.on_resize = self._on_page_resize
    
    def will_unmount(self) -> None:
        """Called when control is unmounted."""
        # Remove listeners
        self.theme_service.remove_theme_change_listener(self._on_theme_change)
        self.i18n_service.remove_language_change_listener(self._on_language_change)
        self.page.on_resize = None
    
    def _on_page_resize(self, e) -> None:
        """Handle page resize."""
        # Rebuild layout if breakpoint changed
        current_use_drawer = should_use_drawer(self.page.width)
        previous_use_drawer = self.navigation_drawer is not None
        
        if current_use_drawer != previous_use_drawer:
            self.update()
