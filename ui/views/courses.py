"""
Courses view - placeholder implementation for demonstration.
"""

import flet as ft
from services.i18n import I18nService


def create_courses_view(i18n_service: I18nService) -> ft.Control:
    """Create the courses view.
    
    Args:
        i18n_service: Internationalization service
        
    Returns:
        Courses view control
    """
    return ft.Container(
        content=ft.Column([
            # Page title
            ft.Text(
                i18n_service.t("navigation.courses"),
                size=32,
                weight=ft.FontWeight.BOLD
            ),
            
            # Page description
            ft.Text(
                i18n_service.t("courses.description"),
                size=16,
                color=ft.Colors.ON_SURFACE_VARIANT
            ),
            
            ft.Container(height=24),  # Spacing
            
            # Content cards
            ft.Row([
                # Physics courses card
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.SCIENCE, size=48, color=ft.Colors.PRIMARY),
                            ft.Text(
                                i18n_service.t("courses.physics"),
                                size=20,
                                weight=ft.FontWeight.BOLD
                            ),
                            ft.Text(
                                i18n_service.t("courses.physics_description"),
                                size=14,
                                color=ft.Colors.ON_SURFACE_VARIANT,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.ElevatedButton(
                                text=i18n_service.t("courses.explore"),
                                icon=ft.Icons.ARROW_FORWARD
                            )
                        ], 
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=12
                        ),
                        padding=24,
                        width=300
                    ),
                    elevation=2
                ),
                
                # Mathematics courses card
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.CALCULATE, size=48, color=ft.Colors.SECONDARY),
                            ft.Text(
                                i18n_service.t("courses.mathematics"),
                                size=20,
                                weight=ft.FontWeight.BOLD
                            ),
                            ft.Text(
                                i18n_service.t("courses.mathematics_description"),
                                size=14,
                                color=ft.Colors.ON_SURFACE_VARIANT,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.ElevatedButton(
                                text=i18n_service.t("courses.explore"),
                                icon=ft.Icons.ARROW_FORWARD
                            )
                        ], 
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=12
                        ),
                        padding=24,
                        width=300
                    ),
                    elevation=2
                ),
                
                # Chemistry courses card
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.BIOTECH, size=48, color=ft.Colors.TERTIARY),
                            ft.Text(
                                i18n_service.t("courses.chemistry"),
                                size=20,
                                weight=ft.FontWeight.BOLD
                            ),
                            ft.Text(
                                i18n_service.t("courses.chemistry_description"),
                                size=14,
                                color=ft.Colors.ON_SURFACE_VARIANT,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.ElevatedButton(
                                text=i18n_service.t("courses.explore"),
                                icon=ft.Icons.ARROW_FORWARD
                            )
                        ], 
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=12
                        ),
                        padding=24,
                        width=300
                    ),
                    elevation=2
                )
            ], 
            wrap=True,
            spacing=16,
            run_spacing=16
            ),
            
            ft.Container(height=32),  # Spacing
            
            # Recent courses section
            ft.Text(
                i18n_service.t("courses.recent"),
                size=24,
                weight=ft.FontWeight.BOLD
            ),
            
            ft.Container(height=16),  # Spacing
            
            # Recent courses list (placeholder)
            ft.Column([
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.PLAY_CIRCLE_OUTLINE),
                    title=ft.Text(i18n_service.t("courses.recent_course_1")),
                    subtitle=ft.Text(i18n_service.t("courses.progress_70")),
                    trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS)
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.PLAY_CIRCLE_OUTLINE),
                    title=ft.Text(i18n_service.t("courses.recent_course_2")),
                    subtitle=ft.Text(i18n_service.t("courses.progress_45")),
                    trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS)
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.PLAY_CIRCLE_OUTLINE),
                    title=ft.Text(i18n_service.t("courses.recent_course_3")),
                    subtitle=ft.Text(i18n_service.t("courses.progress_90")),
                    trailing=ft.Icon(ft.Icons.ARROW_FORWARD_IOS)
                )
            ])
        ], 
        spacing=8,
        scroll=ft.ScrollMode.AUTO
        ),
        padding=ft.padding.all(24),
        expand=True
    )
