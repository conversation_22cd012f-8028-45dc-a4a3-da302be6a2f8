import flet as ft
from typing import Optional

from services.app_state import AppState
from services.i18n import get_i18n_service, t
from config.settings import EducationalTrack


class TrackSelectionView:
    """Educational track selection view for onboarding flow."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.app_state = AppState()
        self.i18n = get_i18n_service()
        self.selected_track: Optional[EducationalTrack] = None
        
        # Initialize with current track if available
        current_track = self.app_state.get_educational_track()
        if current_track:
            self.selected_track = current_track
    
    def build(self) -> ft.View:
        """Build the track selection view."""
        
        # Track selection cards
        track_cards = []
        
        for track in EducationalTrack:
            is_selected = self.selected_track == track
            
            # Get track name and description from i18n
            track_key = track.value.lower()
            track_name = t(f"onboarding.track.tracks.{track_key}.name")
            track_description = t(f"onboarding.track.tracks.{track_key}.description")
            
            card = ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(
                            track_name,
                            size=18,
                            weight=ft.FontWeight.BOLD,
                            text_align=ft.TextAlign.CENTER
                        ),
                        ft.Text(
                            track_description,
                            size=14,
                            color=ft.Colors.GREY_600,
                            text_align=ft.TextAlign.CENTER
                        )
                    ], 
                    alignment=ft.MainAxisAlignment.CENTER,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=8
                    ),
                    padding=ft.padding.all(16),
                    alignment=ft.alignment.center,
                    bgcolor=ft.Colors.BLUE_100 if is_selected else None,
                    border_radius=12,
                    ink=True,
                    on_click=lambda e, track_val=track: self._on_track_selected(track_val)
                ),
                elevation=2 if not is_selected else 8,
                margin=ft.margin.symmetric(vertical=4)
            )
            
            track_cards.append(card)
        
        # Navigation buttons
        back_button = ft.TextButton(
            text=t("common.buttons.back"),
            icon=ft.Icons.ARROW_BACK,
            on_click=self._on_back_clicked,
            style=ft.ButtonStyle(
                padding=ft.padding.symmetric(horizontal=24, vertical=12)
            )
        )

        finish_button = ft.ElevatedButton(
            text=t("common.buttons.finish"),
            icon=ft.Icons.CHECK,
            on_click=self._on_finish_clicked,
            disabled=self.selected_track is None,
            style=ft.ButtonStyle(
                padding=ft.padding.symmetric(horizontal=32, vertical=16),
                text_style=ft.TextStyle(size=16)
            )
        )
        
        # Main content
        content = ft.Column([
            # Header
            ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.SCHOOL,
                        size=64,
                        color=ft.Colors.PRIMARY
                    ),
                    ft.Text(
                        t("onboarding.track.title"),
                        size=28,
                        weight=ft.FontWeight.BOLD,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.Text(
                        t("onboarding.track.description"),
                        size=16,
                        color=ft.Colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    )
                ],
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=16
                ),
                padding=ft.padding.only(bottom=24)
            ),
            
            # Track selection cards
            ft.Container(
                content=ft.Column(
                    track_cards,
                    spacing=8,
                    scroll=ft.ScrollMode.AUTO
                ),
                width=500,
                height=400
            ),
            
            # Navigation buttons
            ft.Container(
                content=ft.Row([
                    back_button,
                    finish_button
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                padding=ft.padding.only(top=24),
                width=500
            )
        ],
        alignment=ft.MainAxisAlignment.CENTER,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        spacing=0
        )
        
        return ft.View(
            route="/onboarding/track",
            controls=[
                ft.Container(
                    content=content,
                    alignment=ft.alignment.center,
                    expand=True,
                    padding=ft.padding.all(32)
                )
            ],
            bgcolor=ft.Colors.SURFACE,
            scroll=ft.ScrollMode.AUTO
        )
    
    def _on_track_selected(self, track: EducationalTrack) -> None:
        """Handle track selection."""
        self.selected_track = track
        
        # Update the view to reflect selection
        self.page.update()
    
    def _on_back_clicked(self, e) -> None:
        """Handle back button click."""
        self.page.go("/onboarding/language")
    
    def _on_finish_clicked(self, e) -> None:
        """Handle finish button click."""
        if self.selected_track:
            # Save track to app state
            self.app_state.set_educational_track(self.selected_track)
            
            # Mark onboarding as complete
            self.app_state.set_first_run(False)
            
            # Save state
            self.app_state.save_state()
            
            # Navigate to home
            self.page.go("/home")


def create_track_selection_view(page: ft.Page) -> ft.View:
    """Factory function to create track selection view."""
    view_instance = TrackSelectionView(page)
    return view_instance.build()
