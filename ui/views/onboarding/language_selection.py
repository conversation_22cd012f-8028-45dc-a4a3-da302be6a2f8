import flet as ft
from typing import Optional

from services.app_state import AppState
from services.i18n import get_i18n_service, t
from config.settings import Language


class LanguageSelectionView:
    """Language selection view for onboarding flow."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.app_state = AppState()
        self.i18n = get_i18n_service()
        self.selected_language: Optional[Language] = None
        
        # Initialize with current language if available
        current_lang = self.app_state.get_language()
        if current_lang:
            self.selected_language = current_lang
    
    def build(self) -> ft.View:
        """Build the language selection view."""
        
        # Language selection cards
        language_cards = []
        
        for language in Language:
            is_selected = self.selected_language == language
            
            # Language display names
            display_names = {
                Language.ARABIC: "العربية",
                Language.FRENCH: "Français"
            }
            
            card = ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(
                            display_names[language],
                            size=20,
                            weight=ft.FontWeight.BOLD,
                            text_align=ft.TextAlign.CENTER
                        ),
                        ft.Text(
                            language.value.title(),
                            size=14,
                            color=ft.Colors.GREY_600,
                            text_align=ft.TextAlign.CENTER
                        )
                    ], 
                    alignment=ft.MainAxisAlignment.CENTER,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=8
                    ),
                    padding=ft.padding.all(20),
                    alignment=ft.alignment.center,
                    bgcolor=ft.Colors.BLUE_100 if is_selected else None,
                    border_radius=12,
                    ink=True,
                    on_click=lambda e, lang=language: self._on_language_selected(lang)
                ),
                elevation=2 if not is_selected else 8,
                margin=ft.margin.symmetric(vertical=8)
            )
            
            language_cards.append(card)
        
        # Next button
        next_button = ft.ElevatedButton(
            text="التالي" if self.selected_language == Language.ARABIC else "Suivant",
            icon=ft.Icons.ARROW_FORWARD,
            on_click=self._on_next_clicked,
            disabled=self.selected_language is None,
            style=ft.ButtonStyle(
                padding=ft.padding.symmetric(horizontal=32, vertical=16),
                text_style=ft.TextStyle(size=16)
            )
        )
        
        # Main content
        content = ft.Column([
            # Header
            ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.LANGUAGE,
                        size=64,
                        color=ft.Colors.PRIMARY
                    ),
                    ft.Text(
                        "اختر لغتك المفضلة" if self.selected_language == Language.ARABIC else "Choisissez votre langue préférée",
                        size=28,
                        weight=ft.FontWeight.BOLD,
                        text_align=ft.TextAlign.CENTER
                    ),
                    ft.Text(
                        "يرجى اختيار اللغة التي تفضل استخدامها في التطبيق" if self.selected_language == Language.ARABIC 
                        else "Veuillez sélectionner la langue que vous préférez utiliser dans l'application",
                        size=16,
                        color=ft.Colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    )
                ],
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=16
                ),
                padding=ft.padding.only(bottom=32)
            ),
            
            # Language selection cards
            ft.Container(
                content=ft.Column(
                    language_cards,
                    spacing=12
                ),
                width=400
            ),
            
            # Next button
            ft.Container(
                content=next_button,
                padding=ft.padding.only(top=32),
                alignment=ft.alignment.center
            )
        ],
        alignment=ft.MainAxisAlignment.CENTER,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        spacing=0
        )
        
        return ft.View(
            route="/onboarding/language",
            controls=[
                ft.Container(
                    content=content,
                    alignment=ft.alignment.center,
                    expand=True,
                    padding=ft.padding.all(32)
                )
            ],
            bgcolor=ft.Colors.SURFACE,
            scroll=ft.ScrollMode.AUTO
        )
    
    def _on_language_selected(self, language: Language) -> None:
        """Handle language selection."""
        self.selected_language = language
        
        # Update the view to reflect selection
        self.page.update()
    
    def _on_next_clicked(self, e) -> None:
        """Handle next button click."""
        if self.selected_language:
            # Save language to app state
            self.app_state.set_language(self.selected_language)
            
            # Update i18n service
            self.i18n.set_language(self.selected_language)
            
            # Set RTL for Arabic
            self.page.rtl = self.selected_language == Language.ARABIC
            
            # Navigate to track selection
            self.page.go("/onboarding/track")


def create_language_selection_view(page: ft.Page) -> ft.View:
    """Factory function to create language selection view."""
    view_instance = LanguageSelectionView(page)
    return view_instance.build()
