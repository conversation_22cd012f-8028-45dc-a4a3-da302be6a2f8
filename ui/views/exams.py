"""
Exams view - placeholder implementation for demonstration.
"""

import flet as ft
from services.i18n import I18nService


def create_exams_view(i18n_service: I18nService) -> ft.Control:
    """Create the exams view.
    
    Args:
        i18n_service: Internationalization service
        
    Returns:
        Exams view control
    """
    return ft.Container(
        content=ft.Column([
            # Page title
            ft.Text(
                i18n_service.t("navigation.exams"),
                size=32,
                weight=ft.FontWeight.BOLD
            ),
            
            # Page description
            ft.Text(
                i18n_service.t("exams.description"),
                size=16,
                color=ft.Colors.ON_SURFACE_VARIANT
            ),
            
            ft.Container(height=24),  # Spacing
            
            # Upcoming exams
            ft.Text(
                i18n_service.t("exams.upcoming"),
                size=24,
                weight=ft.FontWeight.BOLD
            ),
            
            ft.Container(height=16),  # Spacing
            
            ft.Card(
                content=ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.Icons.SCHEDULE, size=48, color=ft.Colors.PRIMARY),
                        ft.Column([
                            ft.Text(
                                i18n_service.t("exams.midterm_physics"),
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            ft.Text(
                                i18n_service.t("exams.date_march_15"),
                                size=14,
                                color=ft.Colors.ON_SURFACE_VARIANT
                            ),
                            ft.Text(
                                i18n_service.t("exams.duration_2hours"),
                                size=14,
                                color=ft.Colors.ON_SURFACE_VARIANT
                            )
                        ], expand=True),
                        ft.ElevatedButton(
                            text=i18n_service.t("exams.prepare"),
                            icon=ft.Icons.BOOK
                        )
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                    padding=20
                ),
                elevation=2
            ),
            
            ft.Container(height=32),  # Spacing
            
            # Exam statistics
            ft.Text(
                i18n_service.t("exams.statistics"),
                size=24,
                weight=ft.FontWeight.BOLD
            ),
            
            ft.Container(height=16),  # Spacing
            
            ft.Row([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("8", size=32, weight=ft.FontWeight.BOLD, color=ft.Colors.PRIMARY),
                            ft.Text(i18n_service.t("exams.completed"), size=14)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=16,
                        width=120
                    )
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("87%", size=32, weight=ft.FontWeight.BOLD, color=ft.Colors.SECONDARY),
                            ft.Text(i18n_service.t("exams.average_score"), size=14)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=16,
                        width=120
                    )
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("A-", size=32, weight=ft.FontWeight.BOLD, color=ft.Colors.TERTIARY),
                            ft.Text(i18n_service.t("exams.current_grade"), size=14)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=16,
                        width=120
                    )
                )
            ], spacing=16),
            
            ft.Container(height=32),  # Spacing
            
            # Past exams
            ft.Text(
                i18n_service.t("exams.past_exams"),
                size=24,
                weight=ft.FontWeight.BOLD
            ),
            
            ft.Container(height=16),  # Spacing
            
            # Past exams list
            ft.Column([
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.ASSIGNMENT_TURNED_IN, color=ft.Colors.GREEN),
                    title=ft.Text(i18n_service.t("exams.mechanics_final")),
                    subtitle=ft.Text(i18n_service.t("exams.score_94")),
                    trailing=ft.Row([
                        ft.Text("A", style=ft.TextStyle(weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN)),
                        ft.Icon(ft.Icons.ARROW_FORWARD_IOS)
                    ], tight=True)
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.ASSIGNMENT_TURNED_IN, color=ft.Colors.GREEN),
                    title=ft.Text(i18n_service.t("exams.thermodynamics_midterm")),
                    subtitle=ft.Text(i18n_service.t("exams.score_82")),
                    trailing=ft.Row([
                        ft.Text("B+", style=ft.TextStyle(weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE)),
                        ft.Icon(ft.Icons.ARROW_FORWARD_IOS)
                    ], tight=True)
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.ASSIGNMENT_TURNED_IN, color=ft.Colors.GREEN),
                    title=ft.Text(i18n_service.t("exams.optics_quiz")),
                    subtitle=ft.Text(i18n_service.t("exams.score_76")),
                    trailing=ft.Row([
                        ft.Text("B", style=ft.TextStyle(weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE)),
                        ft.Icon(ft.Icons.ARROW_FORWARD_IOS)
                    ], tight=True)
                )
            ]),
            
            ft.Container(height=32),  # Spacing
            
            # Study resources
            ft.Text(
                i18n_service.t("exams.study_resources"),
                size=24,
                weight=ft.FontWeight.BOLD
            ),
            
            ft.Container(height=16),  # Spacing
            
            ft.Row([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.BOOK, size=40, color=ft.Colors.PRIMARY),
                            ft.Text(
                                i18n_service.t("exams.study_guides"),
                                size=16,
                                weight=ft.FontWeight.BOLD,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.OutlinedButton(
                                text=i18n_service.t("exams.download"),
                                icon=ft.Icons.DOWNLOAD
                            )
                        ], 
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=8
                        ),
                        padding=16,
                        width=200
                    ),
                    elevation=1
                ),
                
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.QUIZ, size=40, color=ft.Colors.SECONDARY),
                            ft.Text(
                                i18n_service.t("exams.practice_tests"),
                                size=16,
                                weight=ft.FontWeight.BOLD,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.OutlinedButton(
                                text=i18n_service.t("exams.start"),
                                icon=ft.Icons.PLAY_ARROW
                            )
                        ], 
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=8
                        ),
                        padding=16,
                        width=200
                    ),
                    elevation=1
                )
            ], spacing=16)
        ], 
        spacing=8,
        scroll=ft.ScrollMode.AUTO
        ),
        padding=ft.padding.all(24),
        expand=True
    )
