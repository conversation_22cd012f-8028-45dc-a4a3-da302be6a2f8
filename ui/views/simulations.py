"""
Simulations view - placeholder implementation for demonstration.
"""

import flet as ft
from services.i18n import I18nService


def create_simulations_view(i18n_service: I18nService) -> ft.Control:
    """Create the simulations view.
    
    Args:
        i18n_service: Internationalization service
        
    Returns:
        Simulations view control
    """
    return ft.Container(
        content=ft.Column([
            # Page title
            ft.Text(
                i18n_service.t("navigation.simulations"),
                size=32,
                weight=ft.FontWeight.BOLD
            ),
            
            # Page description
            ft.Text(
                i18n_service.t("simulations.description"),
                size=16,
                color=ft.Colors.ON_SURFACE_VARIANT
            ),
            
            ft.Container(height=24),  # Spacing
            
            # Featured simulations
            ft.Text(
                i18n_service.t("simulations.featured"),
                size=24,
                weight=ft.FontWeight.BOLD
            ),
            
            ft.Container(height=16),  # Spacing
            
            ft.Row([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.WAVES, size=48, color=ft.Colors.PRIMARY),
                            ft.Text(
                                i18n_service.t("simulations.wave_interference"),
                                size=18,
                                weight=ft.FontWeight.BOLD,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.Text(
                                i18n_service.t("simulations.wave_description"),
                                size=14,
                                color=ft.Colors.ON_SURFACE_VARIANT,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.ElevatedButton(
                                text=i18n_service.t("simulations.launch"),
                                icon=ft.Icons.PLAY_ARROW
                            )
                        ], 
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=12
                        ),
                        padding=20,
                        width=280
                    ),
                    elevation=2
                ),
                
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.ELECTRIC_BOLT, size=48, color=ft.Colors.SECONDARY),
                            ft.Text(
                                i18n_service.t("simulations.electric_field"),
                                size=18,
                                weight=ft.FontWeight.BOLD,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.Text(
                                i18n_service.t("simulations.electric_description"),
                                size=14,
                                color=ft.Colors.ON_SURFACE_VARIANT,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.ElevatedButton(
                                text=i18n_service.t("simulations.launch"),
                                icon=ft.Icons.PLAY_ARROW
                            )
                        ], 
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=12
                        ),
                        padding=20,
                        width=280
                    ),
                    elevation=2
                )
            ], 
            wrap=True,
            spacing=16,
            run_spacing=16
            ),
            
            ft.Container(height=32),  # Spacing
            
            # Categories
            ft.Text(
                i18n_service.t("simulations.categories"),
                size=24,
                weight=ft.FontWeight.BOLD
            ),
            
            ft.Container(height=16),  # Spacing
            
            ft.Row([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.SPEED, size=40, color=ft.Colors.PRIMARY),
                            ft.Text(
                                i18n_service.t("simulations.mechanics"),
                                size=16,
                                weight=ft.FontWeight.BOLD
                            ),
                            ft.Text("12 " + i18n_service.t("simulations.simulations"), size=12)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=16,
                        width=150
                    ),
                    on_click=lambda e: print("Mechanics clicked")
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.THERMOSTAT, size=40, color=ft.Colors.SECONDARY),
                            ft.Text(
                                i18n_service.t("simulations.thermodynamics"),
                                size=16,
                                weight=ft.FontWeight.BOLD
                            ),
                            ft.Text("8 " + i18n_service.t("simulations.simulations"), size=12)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=16,
                        width=150
                    ),
                    on_click=lambda e: print("Thermodynamics clicked")
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.LIGHTBULB, size=40, color=ft.Colors.TERTIARY),
                            ft.Text(
                                i18n_service.t("simulations.optics"),
                                size=16,
                                weight=ft.FontWeight.BOLD
                            ),
                            ft.Text("6 " + i18n_service.t("simulations.simulations"), size=12)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=16,
                        width=150
                    ),
                    on_click=lambda e: print("Optics clicked")
                )
            ], spacing=16),
            
            ft.Container(height=32),  # Spacing
            
            # Recent simulations
            ft.Text(
                i18n_service.t("simulations.recent"),
                size=24,
                weight=ft.FontWeight.BOLD
            ),
            
            ft.Container(height=16),  # Spacing
            
            # Recent simulations list
            ft.Column([
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.PLAY_CIRCLE_OUTLINE, color=ft.Colors.PRIMARY),
                    title=ft.Text(i18n_service.t("simulations.pendulum_motion")),
                    subtitle=ft.Text(i18n_service.t("simulations.last_used_2_days")),
                    trailing=ft.Row([
                        ft.Icon(ft.Icons.STAR, color=ft.Colors.AMBER, size=16),
                        ft.Text("4.7"),
                        ft.Icon(ft.Icons.ARROW_FORWARD_IOS)
                    ], tight=True)
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.PLAY_CIRCLE_OUTLINE, color=ft.Colors.SECONDARY),
                    title=ft.Text(i18n_service.t("simulations.projectile_motion")),
                    subtitle=ft.Text(i18n_service.t("simulations.last_used_5_days")),
                    trailing=ft.Row([
                        ft.Icon(ft.Icons.STAR, color=ft.Colors.AMBER, size=16),
                        ft.Text("4.9"),
                        ft.Icon(ft.Icons.ARROW_FORWARD_IOS)
                    ], tight=True)
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.PLAY_CIRCLE_OUTLINE, color=ft.Colors.TERTIARY),
                    title=ft.Text(i18n_service.t("simulations.circuit_builder")),
                    subtitle=ft.Text(i18n_service.t("simulations.last_used_1_week")),
                    trailing=ft.Row([
                        ft.Icon(ft.Icons.STAR, color=ft.Colors.AMBER, size=16),
                        ft.Text("4.5"),
                        ft.Icon(ft.Icons.ARROW_FORWARD_IOS)
                    ], tight=True)
                )
            ])
        ], 
        spacing=8,
        scroll=ft.ScrollMode.AUTO
        ),
        padding=ft.padding.all(24),
        expand=True
    )
