"""
Utility functions for navigation-related operations.
"""

from typing import Optional
from config.settings import NAVIGATION_ITEMS


def route_to_navigation_item(route: str) -> Optional[int]:
    """Map a route to its corresponding navigation item index.
    
    Args:
        route: Route path
        
    Returns:
        Navigation item index or None if not found
    """
    # Remove leading slash and query parameters
    clean_route = route.lstrip('/').split('?')[0]
    
    # Handle empty route (home)
    if not clean_route or clean_route == 'home':
        clean_route = 'home'
    
    # Find matching navigation item
    for i, item in enumerate(NAVIGATION_ITEMS):
        item_route = item.route.lstrip('/')
        if item_route == clean_route:
            return i
    
    return None


def get_selected_index(route: str) -> int:
    """Get the selected navigation index for a route.
    
    Args:
        route: Current route
        
    Returns:
        Selected navigation index (0 if not found)
    """
    index = route_to_navigation_item(route)
    return index if index is not None else 0


def is_navigation_route(route: str) -> bool:
    """Check if a route corresponds to a navigation item.
    
    Args:
        route: Route to check
        
    Returns:
        True if route is a navigation route
    """
    return route_to_navigation_item(route) is not None


def get_navigation_route(index: int) -> str:
    """Get the route for a navigation item index.
    
    Args:
        index: Navigation item index
        
    Returns:
        Route path or empty string if invalid index
    """
    if 0 <= index < len(NAVIGATION_ITEMS):
        return NAVIGATION_ITEMS[index].route
    return ""


def get_navigation_label_key(index: int) -> str:
    """Get the label key for a navigation item index.
    
    Args:
        index: Navigation item index
        
    Returns:
        Label key or empty string if invalid index
    """
    if 0 <= index < len(NAVIGATION_ITEMS):
        return NAVIGATION_ITEMS[index].label_key
    return ""


def get_navigation_icon(index: int) -> str:
    """Get the icon for a navigation item index.
    
    Args:
        index: Navigation item index
        
    Returns:
        Icon name or empty string if invalid index
    """
    if 0 <= index < len(NAVIGATION_ITEMS):
        return NAVIGATION_ITEMS[index].icon
    return ""


def get_navigation_selected_icon(index: int) -> Optional[str]:
    """Get the selected icon for a navigation item index.
    
    Args:
        index: Navigation item index
        
    Returns:
        Selected icon name or None if not set or invalid index
    """
    if 0 <= index < len(NAVIGATION_ITEMS):
        return NAVIGATION_ITEMS[index].selected_icon
    return None
