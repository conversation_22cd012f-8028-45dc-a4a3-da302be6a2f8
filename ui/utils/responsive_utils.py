"""
Utility functions for responsive design calculations and breakpoint management.
"""

from enum import Enum
from typing import Optional
from config.settings import RESPONSIVE_BREAKPOINTS


class ScreenSize(Enum):
    """Screen size categories."""
    MOBILE = "mobile"
    TABLET = "tablet"
    DESKTOP = "desktop"


def get_screen_size_category(width: Optional[float]) -> ScreenSize:
    """Determine screen size category based on width.
    
    Args:
        width: Screen width in pixels
        
    Returns:
        Screen size category
    """
    if width is None:
        return ScreenSize.DESKTOP  # Default to desktop
    
    if width < RESPONSIVE_BREAKPOINTS["tablet"]:
        return ScreenSize.MOBILE
    elif width < RESPONSIVE_BREAKPOINTS["desktop"]:
        return ScreenSize.TABLET
    else:
        return ScreenSize.DESKTOP


def is_mobile(width: Optional[float]) -> bool:
    """Check if screen width is mobile size.
    
    Args:
        width: Screen width in pixels
        
    Returns:
        True if mobile size
    """
    return get_screen_size_category(width) == ScreenSize.MOBILE


def is_tablet(width: Optional[float]) -> bool:
    """Check if screen width is tablet size.
    
    Args:
        width: Screen width in pixels
        
    Returns:
        True if tablet size
    """
    return get_screen_size_category(width) == ScreenSize.TABLET


def is_desktop(width: Optional[float]) -> bool:
    """Check if screen width is desktop size.
    
    Args:
        width: Screen width in pixels
        
    Returns:
        True if desktop size
    """
    return get_screen_size_category(width) == ScreenSize.DESKTOP


def should_use_drawer(width: Optional[float]) -> bool:
    """Determine if navigation drawer should be used instead of rail.
    
    Args:
        width: Screen width in pixels
        
    Returns:
        True if drawer should be used
    """
    return not is_desktop(width)


def calculate_responsive_width(
    width: Optional[float], 
    mobile_width: float, 
    tablet_width: float, 
    desktop_width: float
) -> float:
    """Calculate responsive width based on screen size.
    
    Args:
        width: Screen width in pixels
        mobile_width: Width for mobile screens
        tablet_width: Width for tablet screens
        desktop_width: Width for desktop screens
        
    Returns:
        Calculated responsive width
    """
    screen_size = get_screen_size_category(width)
    
    if screen_size == ScreenSize.MOBILE:
        return mobile_width
    elif screen_size == ScreenSize.TABLET:
        return tablet_width
    else:
        return desktop_width


def calculate_responsive_padding(width: Optional[float]) -> float:
    """Calculate responsive padding based on screen size.
    
    Args:
        width: Screen width in pixels
        
    Returns:
        Padding value
    """
    return calculate_responsive_width(
        width,
        mobile_width=16.0,    # Mobile padding
        tablet_width=24.0,    # Tablet padding
        desktop_width=32.0    # Desktop padding
    )


def calculate_responsive_spacing(width: Optional[float]) -> float:
    """Calculate responsive spacing based on screen size.
    
    Args:
        width: Screen width in pixels
        
    Returns:
        Spacing value
    """
    return calculate_responsive_width(
        width,
        mobile_width=8.0,     # Mobile spacing
        tablet_width=12.0,    # Tablet spacing
        desktop_width=16.0    # Desktop spacing
    )


def get_content_max_width(width: Optional[float]) -> Optional[float]:
    """Get maximum content width for responsive design.
    
    Args:
        width: Screen width in pixels
        
    Returns:
        Maximum content width or None for no limit
    """
    if is_desktop(width) and width and width > 1400:
        return 1200.0  # Limit content width on very large screens
    return None


def should_use_compact_layout(width: Optional[float]) -> bool:
    """Determine if compact layout should be used.
    
    Args:
        width: Screen width in pixels
        
    Returns:
        True if compact layout should be used
    """
    return is_mobile(width)


def get_grid_columns(width: Optional[float]) -> int:
    """Get number of grid columns based on screen size.
    
    Args:
        width: Screen width in pixels
        
    Returns:
        Number of grid columns
    """
    screen_size = get_screen_size_category(width)
    
    if screen_size == ScreenSize.MOBILE:
        return 1
    elif screen_size == ScreenSize.TABLET:
        return 2
    else:
        return 3
