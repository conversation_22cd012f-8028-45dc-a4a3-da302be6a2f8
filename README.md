# Hiel Physics 🧪⚛️

**Hiel Physics** is a comprehensive physics education application designed specifically for Moroccan high school students. The app provides interactive learning experiences, simulations, and assessments across multiple academic tracks, supporting both Arabic and French languages.

## 🎯 Purpose

Hiel Physics aims to revolutionize physics education in Morocco by providing:

- **Interactive Learning**: Engaging physics simulations and visualizations
- **Comprehensive Coverage**: Content aligned with Moroccan high school curriculum
- **Bilingual Support**: Full Arabic and French language support
- **Track-Specific Content**: Tailored materials for different academic specializations
- **Assessment Tools**: Quizzes, exercises, and exam preparation materials

## 🎓 Supported Academic Tracks

The application supports all major Moroccan high school physics tracks:

### 🔬 **Sciences Mathématiques A (SM-A)**
- Advanced mathematical physics concepts
- Theoretical foundations and problem-solving
- Preparation for engineering and mathematics studies

### 🔬 **Sciences Mathématiques B (SM-B)**
- Mathematical physics with practical applications
- Balance between theory and experimentation
- Engineering and applied sciences focus

### ⚗️ **Sciences Physiques Chimie (SPC)**
- Integrated physics and chemistry curriculum
- Laboratory experiments and practical work
- Preparation for physical sciences studies

### 🌱 **Sciences de la Vie et de la Terre (SVT)**
- Physics concepts relevant to life sciences
- Biophysics and environmental physics
- Medical and biological sciences preparation

### ⚙️ **Sciences et Technologies (Technical)**
- Applied physics for technical fields
- Industrial and technological applications
- Vocational and technical education focus

## 🌐 Language Support

- **Arabic (العربية)**: Primary language support with right-to-left text rendering
- **French (Français)**: Secondary language support for bilingual education
- **Dynamic Language Switching**: Users can switch between languages seamlessly

## 🚀 Features

- **Interactive Simulations**: Physics concepts brought to life with Manim animations
- **Comprehensive Courses**: Structured learning paths for each track
- **Practice Exercises**: Hands-on problems with step-by-step solutions
- **Assessment Tools**: Quizzes and exams with instant feedback
- **Progress Tracking**: Monitor learning progress and achievements
- **Offline Support**: Core content available without internet connection

## 🛠️ Technology Stack

- **Frontend Framework**: [Flet](https://flet.dev/) - Modern Python UI framework
- **Animations**: [Manim Community Edition](https://www.manim.community/) - Mathematical animations
- **Internationalization**: Babel for multi-language support
- **Configuration**: YAML-based configuration management
- **Build System**: Modern Python packaging with pyproject.toml

## 📋 Requirements

### System Requirements
- **Python**: 3.9 or higher
- **Operating System**: 
  - Windows 10+ (64-bit)
  - macOS 11+ (Big Sur or later)
  - Linux (Debian 11+, Ubuntu 20.04 LTS+)

### Dependencies
- **Flet**: Modern UI framework for Python
- **Manim Community Edition**: Mathematical animation engine
- **Babel**: Internationalization support
- **PyYAML**: Configuration file management
- **Additional**: FFmpeg (for Manim video rendering)

## 🔧 Installation

### 1. Clone the Repository
```bash
git clone https://github.com/your-username/hiel_physics.git
cd hiel_physics
```

### 2. Create Virtual Environment
```bash
python -m venv venv

# On Windows
venv\Scripts\activate

# On macOS/Linux
source venv/bin/activate
```

### 3. Install Dependencies
```bash
pip install -e .
```

### 4. Install System Dependencies

#### FFmpeg (Required for Manim)
- **Windows**: Download from [FFmpeg website](https://ffmpeg.org/download.html)
- **macOS**: `brew install ffmpeg`
- **Linux**: `sudo apt-get install ffmpeg` (Ubuntu/Debian)

#### LaTeX (Optional, for mathematical formulas)
- **Windows**: Install MiKTeX or TeX Live
- **macOS**: `brew install --cask mactex`
- **Linux**: `sudo apt-get install texlive-full`

### 5. Run the Application
```bash
python main.py
```

## 📁 Project Structure

```
hiel_physics/
├── main.py                 # Application entry point
├── pyproject.toml         # Project configuration and dependencies
├── README.md              # Project documentation
├── .gitignore            # Git ignore patterns
│
├── ui/                   # User Interface Components
│   ├── __init__.py
│   ├── router.py         # Navigation and routing system
│   ├── components/       # Reusable UI components
│   │   └── __init__.py
│   └── views/           # Application views/pages
│       ├── __init__.py
│       └── home.py      # Home page view
│
├── models/              # Data models and business logic
│   └── __init__.py
│
├── services/            # Business logic and state management
│   ├── __init__.py
│   └── app_state.py    # Application state management
│
├── config/              # Configuration management
│   ├── __init__.py
│   └── settings.py     # Application settings
│
├── assets/              # Static resources
│   ├── images/         # Image assets
│   └── icons/          # Icon assets
│
└── locales/            # Internationalization files
    ├── ar/             # Arabic translations
    └── fr/             # French translations
```

## 🔄 Development Workflow

### Setting Up Development Environment
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Install development dependencies: `pip install -e .[dev]`
4. Make your changes
5. Run tests: `pytest`
6. Submit a pull request

### Code Style
- Follow PEP 8 Python style guidelines
- Use type hints where appropriate
- Write descriptive commit messages
- Add docstrings to functions and classes

### Testing
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=hiel_physics

# Run specific test file
pytest tests/test_specific_module.py
```

## 🌍 Internationalization

The application supports Arabic and French through Babel:

### Adding New Translations
1. Extract translatable strings: `pybabel extract -F babel.cfg -o locales/messages.pot .`
2. Update language files: `pybabel update -i locales/messages.pot -d locales`
3. Translate strings in `.po` files
4. Compile translations: `pybabel compile -d locales`

### Language Files Structure
```
locales/
├── ar/LC_MESSAGES/
│   ├── messages.po     # Arabic translations
│   └── messages.mo     # Compiled Arabic
└── fr/LC_MESSAGES/
    ├── messages.po     # French translations
    └── messages.mo     # Compiled French
```

## 🤝 Contributing

We welcome contributions from educators, developers, and students! Here's how you can help:

### For Educators
- Review content accuracy for different tracks
- Suggest new physics topics or concepts
- Provide feedback on pedagogical approaches
- Test the application with students

### For Developers
- Implement new features and improvements
- Fix bugs and optimize performance
- Enhance UI/UX design
- Add new simulation capabilities

### For Students
- Report bugs and usability issues
- Suggest new features or improvements
- Provide feedback on learning experience
- Help with testing and quality assurance

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Manim Community**: For the amazing mathematical animation framework
- **Flet Team**: For the modern Python UI framework
- **Moroccan Ministry of Education**: For curriculum guidelines and standards
- **Physics Educators**: For their valuable input and feedback

## 📞 Support

- **Issues**: Report bugs and feature requests on [GitHub Issues](https://github.com/your-username/hiel_physics/issues)
- **Discussions**: Join community discussions on [GitHub Discussions](https://github.com/your-username/hiel_physics/discussions)
- **Email**: Contact <NAME_EMAIL>

## 🗺️ Roadmap

### Phase 1: Foundation (Current)
- ✅ Project setup and basic structure
- ✅ Core navigation and routing
- ✅ Internationalization framework
- 🔄 Basic UI components

### Phase 2: Content Development
- 📋 Course content for all tracks
- 📋 Interactive simulations
- 📋 Exercise and quiz systems
- 📋 Assessment tools

### Phase 3: Advanced Features
- 📋 Progress tracking and analytics
- 📋 Offline content support
- 📋 Advanced simulations
- 📋 Teacher dashboard

### Phase 4: Enhancement
- 📋 Mobile app version
- 📋 Cloud synchronization
- 📋 Collaborative features
- 📋 AI-powered recommendations

---

**Made with ❤️ for Moroccan students and educators**

*Hiel Physics - Making physics accessible, engaging, and effective for everyone.*