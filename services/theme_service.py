"""
Theme service for managing application themes and Material Design styling.
"""

import flet as ft
from typing import Dict, Any, Callable, Optional
from enum import Enum

from config.settings import (
    THEME_COLORS, DEFAULT_USER_PREFERENCES, ANIMATION_DURATION_MS
)
from services.app_state import AppState


class ThemeMode(Enum):
    """Theme mode enumeration."""
    LIGHT = "light"
    DARK = "dark"
    SYSTEM = "system"


class ThemeService:
    """Service for managing application themes and Material Design styling."""
    
    def __init__(self, app_state: AppState):
        """Initialize the theme service.
        
        Args:
            app_state: Application state service for persistence
        """
        self.app_state = app_state
        self._theme_change_listeners: list[Callable] = []
        self._current_theme_mode = ThemeMode.LIGHT
        self._system_theme_mode = ThemeMode.LIGHT
        
        # Load saved theme mode
        saved_mode = self.app_state.get_theme_mode()
        if saved_mode:
            try:
                self._current_theme_mode = ThemeMode(saved_mode)
            except ValueError:
                self._current_theme_mode = ThemeMode.LIGHT
    
    def get_theme_mode(self) -> ThemeMode:
        """Get the current theme mode."""
        return self._current_theme_mode
    
    def set_theme_mode(self, mode: ThemeMode) -> None:
        """Set the theme mode and notify listeners.
        
        Args:
            mode: Theme mode to set
        """
        if self._current_theme_mode != mode:
            self._current_theme_mode = mode
            self.app_state.set_theme_mode(mode.value)
            self._notify_theme_change()
    
    def get_effective_theme_mode(self) -> ThemeMode:
        """Get the effective theme mode (resolves system mode)."""
        if self._current_theme_mode == ThemeMode.SYSTEM:
            return self._system_theme_mode
        return self._current_theme_mode
    
    def set_system_theme_mode(self, mode: ThemeMode) -> None:
        """Set the system theme mode (for system theme resolution).
        
        Args:
            mode: System theme mode (light or dark only)
        """
        if mode in [ThemeMode.LIGHT, ThemeMode.DARK]:
            self._system_theme_mode = mode
            if self._current_theme_mode == ThemeMode.SYSTEM:
                self._notify_theme_change()
    
    def get_current_theme(self) -> ft.Theme:
        """Get the current Flet theme object."""
        effective_mode = self.get_effective_theme_mode()
        if effective_mode == ThemeMode.DARK:
            return self.create_dark_theme()
        return self.create_light_theme()
    
    def create_light_theme(self) -> ft.Theme:
        """Create a light theme with Material Design colors."""
        colors = THEME_COLORS["light"]

        return ft.Theme(
            color_scheme_seed=colors["primary"],
            use_material3=True
        )
    
    def create_dark_theme(self) -> ft.Theme:
        """Create a dark theme with Material Design colors."""
        colors = THEME_COLORS["dark"]

        return ft.Theme(
            color_scheme_seed=colors["primary"],
            use_material3=True
        )
    
    def get_theme_colors(self) -> Dict[str, str]:
        """Get the current theme colors dictionary."""
        effective_mode = self.get_effective_theme_mode()
        return THEME_COLORS[effective_mode.value]
    
    def add_theme_change_listener(self, listener: Callable) -> None:
        """Add a theme change listener.
        
        Args:
            listener: Function to call when theme changes
        """
        if listener not in self._theme_change_listeners:
            self._theme_change_listeners.append(listener)
    
    def remove_theme_change_listener(self, listener: Callable) -> None:
        """Remove a theme change listener.
        
        Args:
            listener: Function to remove from listeners
        """
        if listener in self._theme_change_listeners:
            self._theme_change_listeners.remove(listener)
    
    def _notify_theme_change(self) -> None:
        """Notify all listeners of theme change."""
        for listener in self._theme_change_listeners:
            try:
                listener()
            except Exception as e:
                print(f"Error in theme change listener: {e}")
