import json
import os
from typing import Dict, Any, Optional, Callable, List
from pathlib import Path

from services.app_state import AppState
from config.settings import Language


class I18nService:
    """Internationalization service for managing language catalogs and translations."""
    
    def __init__(self):
        self._catalogs: Dict[str, Dict[str, Any]] = {}
        self._current_language: Language = Language.ARABIC
        self._listeners: List[Callable[[Language], None]] = []
        self._fallback_language: Language = Language.ARABIC
        self._app_state = AppState()
        
        # Load initial language from app state
        self._current_language = self._app_state.get_language()
        
        # Load all available catalogs
        self._load_catalogs()
    
    def _load_catalogs(self) -> None:
        """Load all language catalogs from the locales directory."""
        locales_dir = Path("locales")
        
        if not locales_dir.exists():
            print(f"Warning: Locales directory {locales_dir} not found")
            return
        
        for language in Language:
            catalog_file = locales_dir / f"{language.value}.json"
            if catalog_file.exists():
                try:
                    with open(catalog_file, 'r', encoding='utf-8') as f:
                        self._catalogs[language.value] = json.load(f)
                    print(f"Loaded catalog for {language.value}")
                except Exception as e:
                    print(f"Error loading catalog for {language.value}: {e}")
            else:
                print(f"Warning: Catalog file {catalog_file} not found")
    
    def get_current_language(self) -> Language:
        """Get the current language."""
        return self._current_language
    
    def set_language(self, language: Language) -> None:
        """Set the current language and notify listeners."""
        if language != self._current_language:
            self._current_language = language
            self._app_state.set_language(language)
            self._notify_listeners(language)
    
    def add_language_change_listener(self, listener: Callable[[Language], None]) -> None:
        """Add a listener for language change events."""
        self._listeners.append(listener)
    
    def remove_language_change_listener(self, listener: Callable[[Language], None]) -> None:
        """Remove a language change listener."""
        if listener in self._listeners:
            self._listeners.remove(listener)
    
    def _notify_listeners(self, language: Language) -> None:
        """Notify all listeners of language change."""
        for listener in self._listeners:
            try:
                listener(language)
            except Exception as e:
                print(f"Error notifying language change listener: {e}")
    
    def get_text(self, key: str, **kwargs) -> str:
        """
        Get translated text for the given key.
        
        Args:
            key: Dot-notation key (e.g., 'onboarding.language.title')
            **kwargs: Parameters for string formatting
            
        Returns:
            Translated text with parameters substituted
        """
        # Try current language first
        text = self._get_text_from_catalog(self._current_language.value, key)
        
        # Fallback to fallback language if not found
        if text is None and self._current_language != self._fallback_language:
            text = self._get_text_from_catalog(self._fallback_language.value, key)
        
        # Final fallback to key itself
        if text is None:
            text = key
            print(f"Warning: Translation not found for key '{key}'")
        
        # Apply parameter substitution
        if kwargs:
            try:
                text = text.format(**kwargs)
            except Exception as e:
                print(f"Error formatting text for key '{key}': {e}")
        
        return text
    
    def _get_text_from_catalog(self, language: str, key: str) -> Optional[str]:
        """Get text from a specific language catalog."""
        if language not in self._catalogs:
            return None
        
        catalog = self._catalogs[language]
        keys = key.split('.')
        
        current = catalog
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return None
        
        return current if isinstance(current, str) else None
    
    def get_available_languages(self) -> List[Language]:
        """Get list of available languages with loaded catalogs."""
        available = []
        for language in Language:
            if language.value in self._catalogs:
                available.append(language)
        return available
    
    def is_rtl(self) -> bool:
        """Check if current language is right-to-left."""
        return self._current_language == Language.ARABIC
    
    def reload_catalogs(self) -> None:
        """Reload all language catalogs."""
        self._catalogs.clear()
        self._load_catalogs()


# Global instance
_i18n_service = None

def get_i18n_service() -> I18nService:
    """Get the global i18n service instance."""
    global _i18n_service
    if _i18n_service is None:
        _i18n_service = I18nService()
    return _i18n_service

def t(key: str, **kwargs) -> str:
    """Shorthand function for getting translated text."""
    return get_i18n_service().get_text(key, **kwargs)
