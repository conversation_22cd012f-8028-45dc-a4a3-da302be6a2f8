[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "hiel_physics"
version = "0.1.0"
description = "Interactive Physics Education App for Moroccan High School Students"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Hiel Physics Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Hiel Physics Team", email = "<EMAIL>"}
]
keywords = [
    "physics",
    "education",
    "morocco",
    "high-school",
    "interactive",
    "flet",
    "manim",
    "bilingual"
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Education",
    "Topic :: Scientific/Engineering :: Physics",
    "Topic :: Software Development :: User Interfaces",
    "Natural Language :: Arabic",
    "Natural Language :: French"
]
requires-python = ">=3.8"
dependencies = [
    "flet>=0.28.3",
    "manim>=0.19.0",
    "babel>=2.17.0",
    "PyYAML>=6.0.2",
    "typing-extensions>=4.0.0",
    "dataclasses-json>=0.6.0",
    "pydantic>=2.0.0",
    "requests>=2.31.0",
    "pillow>=10.0.0",
    "numpy>=1.24.0",
    "matplotlib>=3.7.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0"
]
docs = [
    "sphinx>=6.0.0",
    "sphinx-rtd-theme>=1.2.0",
    "myst-parser>=1.0.0"
]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "pytest-asyncio>=0.21.0"
]

[project.urls]
Homepage = "https://github.com/hielphysics/hiel_physics"
Documentation = "https://hielphysics.readthedocs.io"
Repository = "https://github.com/hielphysics/hiel_physics.git"
"Bug Tracker" = "https://github.com/hielphysics/hiel_physics/issues"
Changelog = "https://github.com/hielphysics/hiel_physics/blob/main/CHANGELOG.md"

[project.scripts]
hiel-physics = "hiel_physics.main:main"

[project.gui-scripts]
hiel-physics-gui = "hiel_physics.main:main"

[tool.setuptools]
package-dir = {"" = "."}

[tool.setuptools.packages.find]
where = ["."]
include = ["hiel_physics*"]
exclude = ["tests*", "docs*", "build*", "dist*"]

[tool.setuptools.package-data]
hiel_physics = [
    "assets/**/*",
    "locales/**/*",
    "config/**/*.yaml",
    "config/**/*.yml",
    "config/**/*.json"
]

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["hiel_physics"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "flet.*",
    "manim.*",
    "babel.*"
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests"
]

[tool.coverage.run]
source = ["hiel_physics"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
    "*/venv/*",
    "*/.venv/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]